package main

import (
	"log"

	"program-manager/config"
	"program-manager/diurnal"
)

func main() {
	log.Println("=== Diurnal Overlap Detection Test ===")

	// Load the test configuration
	diurnalConfig, err := config.LoadDiurnalConfig("config/diurnalConfigOverlapTest.json")
	if err != nil {
		log.Fatalf("Failed to load test configuration: %v", err)
	}

	log.Printf("Loaded configuration with %d instances", len(diurnalConfig.Instances))

	// Print original period statuses
	log.Println("\n--- Original Period Statuses ---")
	for _, instance := range diurnalConfig.Instances {
		log.Printf("Instance %s (%s):", instance.InstanceId, instance.ProgramName)
		for _, period := range instance.Periods {
			log.Printf("  Period %s (%s): %s - %s [STATUS: %s]",
				period.PeriodId, period.Name, period.StartTime, period.EndTime, period.PeriodStatus)
		}
	}

	// Run overlap detection
	log.Println("\n--- Running Overlap Detection ---")
	diurnal.DetectAndHandleOverlaps(&diurnalConfig, nil)

	// Print updated period statuses
	log.Println("\n--- Updated Period Statuses ---")
	for _, instance := range diurnalConfig.Instances {
		log.Printf("Instance %s (%s):", instance.InstanceId, instance.ProgramName)
		for _, period := range instance.Periods {
			status := period.PeriodStatus
			if status == "notUsedOverlap" {
				status = status + " ⚠️"
			}
			log.Printf("  Period %s (%s): %s - %s [STATUS: %s]",
				period.PeriodId, period.Name, period.StartTime, period.EndTime, status)
		}
	}

	// Print overlap status summary
	diurnal.PrintPeriodOverlapStatus(diurnalConfig)

	log.Println("\n=== Test Complete ===")
	log.Println("Expected result: 'earlyAfternoon' period should be marked as 'notUsedOverlap'")
	log.Println("because it starts only 20 minutes after the 'morning' period ends (< 30 min threshold)")
}
