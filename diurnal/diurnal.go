package diurnal

import (
	"context"
	"fmt"
	"log"
	"strings"
	"sync"
	"time"

	"program-manager/redis"
	"program-manager/types"
)

// TimeResolver handles all time resolution logic including dawn/dusk calculations
type TimeResolver struct {
	clockState *types.ClockState
	mutex      sync.RWMutex
}

// NewTimeResolver creates a new TimeResolver instance
func NewTimeResolver(clockState *types.ClockState) *TimeResolver {
	return &TimeResolver{
		clockState: clockState,
	}
}

// SetClockState updates the clock state for time calculations
func (tr *TimeResolver) SetClockState(clockState *types.ClockState) {
	tr.mutex.Lock()
	defer tr.mutex.Unlock()
	tr.clockState = clockState
}

// GetClockState returns the current clock state
func (tr *TimeResolver) GetClockState() *types.ClockState {
	tr.mutex.RLock()
	defer tr.mutex.RUnlock()
	return tr.clockState
}

// ResolveDawnDusk returns dawn and dusk times, either from clock state or defaults
func (tr *TimeResolver) ResolveDawnDusk(referenceTime time.Time) (dawn, dusk time.Time) {
	tr.mutex.RLock()
	defer tr.mutex.RUnlock()

	if tr.clockState != nil {
		// Parse dawn and dusk times from clock state (try both HH:MM:SS and HH:MM formats)
		if d, err := time.Parse("15:04:05", tr.clockState.Dawn); err == nil {
			dawn = time.Date(referenceTime.Year(), referenceTime.Month(), referenceTime.Day(),
				d.Hour(), d.Minute(), 0, 0, referenceTime.Location())
		} else if d, err := time.Parse("15:04", tr.clockState.Dawn); err == nil {
			dawn = time.Date(referenceTime.Year(), referenceTime.Month(), referenceTime.Day(),
				d.Hour(), d.Minute(), 0, 0, referenceTime.Location())
		} else {
			log.Printf("Warning: Failed to parse dawn time '%s', using default: %v", tr.clockState.Dawn, err)
			dawn = time.Date(referenceTime.Year(), referenceTime.Month(), referenceTime.Day(),
				6, 30, 0, 0, referenceTime.Location())
		}

		if d, err := time.Parse("15:04:05", tr.clockState.Dusk); err == nil {
			dusk = time.Date(referenceTime.Year(), referenceTime.Month(), referenceTime.Day(),
				d.Hour(), d.Minute(), 0, 0, referenceTime.Location())
		} else if d, err := time.Parse("15:04", tr.clockState.Dusk); err == nil {
			dusk = time.Date(referenceTime.Year(), referenceTime.Month(), referenceTime.Day(),
				d.Hour(), d.Minute(), 0, 0, referenceTime.Location())
		} else {
			log.Printf("Warning: Failed to parse dusk time '%s', using default: %v", tr.clockState.Dusk, err)
			dusk = time.Date(referenceTime.Year(), referenceTime.Month(), referenceTime.Day(),
				18, 45, 0, 0, referenceTime.Location())
		}
	} else {
		// Use default dawn/dusk times if no clock state available
		dawn = time.Date(referenceTime.Year(), referenceTime.Month(), referenceTime.Day(),
			6, 30, 0, 0, referenceTime.Location())
		dusk = time.Date(referenceTime.Year(), referenceTime.Month(), referenceTime.Day(),
			18, 45, 0, 0, referenceTime.Location())
		log.Printf("Warning: Using default dawn/dusk times (06:30/18:45) - clock state not available")
	}

	return dawn, dusk
}

// Global time resolver instance for backward compatibility
var globalTimeResolver *TimeResolver

// setGlobalClockState sets the global clock state for relative time calculations (backward compatibility)
func setGlobalClockState(clockState *types.ClockState) {
	if globalTimeResolver == nil {
		globalTimeResolver = NewTimeResolver(clockState)
	} else {
		globalTimeResolver.SetClockState(clockState)
	}
}

// getGlobalClockState gets the global clock state for relative time calculations (internal)
func getGlobalClockState() *types.ClockState {
	if globalTimeResolver == nil {
		return nil
	}
	return globalTimeResolver.GetClockState()
}

// GetGlobalClockState gets the global clock state for relative time calculations (exported)
func GetGlobalClockState() *types.ClockState {
	return getGlobalClockState()
}

// ResolveRelativeTime converts relative time to absolute time using dawn/dusk from TimeResolver
func (tr *TimeResolver) ResolveRelativeTime(timeStr string, referenceTime time.Time) (string, error) {
	// Check if it's a relative time
	if !strings.Contains(timeStr, "beforeDawn") && !strings.Contains(timeStr, "afterDawn") &&
		!strings.Contains(timeStr, "beforeDusk") && !strings.Contains(timeStr, "afterDusk") {
		return timeStr, nil // Return as-is if not relative
	}

	// Get dawn and dusk times
	dawnTime, duskTime := tr.ResolveDawnDusk(referenceTime)

	// Parse the time and reference
	parts := strings.Fields(timeStr)
	if len(parts) != 2 {
		return "", fmt.Errorf("invalid relative time format: %s", timeStr)
	}

	timePart := parts[0]
	reference := parts[1]

	// Parse the time offset
	offsetTime, err := time.Parse("15:04", timePart)
	if err != nil {
		return "", fmt.Errorf("invalid time in relative format: %v", err)
	}

	offset := time.Duration(offsetTime.Hour())*time.Hour + time.Duration(offsetTime.Minute())*time.Minute

	var baseTime time.Time
	switch reference {
	case "beforeDawn":
		baseTime = dawnTime.Add(-offset)
	case "afterDawn":
		baseTime = dawnTime.Add(offset)
	case "beforeDusk":
		baseTime = duskTime.Add(-offset)
	case "afterDusk":
		baseTime = duskTime.Add(offset)
	default:
		return "", fmt.Errorf("invalid time reference: %s", reference)
	}

	return baseTime.Format("15:04"), nil
}

// CalculateRampRate calculates the ramp rate between two periods for a given setpoint
func CalculateRampRate(period1, period2 types.Period, setpointName string) (float64, error) {
	// Use global time resolver for backward compatibility
	if globalTimeResolver == nil {
		globalTimeResolver = NewTimeResolver(getGlobalClockState())
	}

	now := time.Now()

	// Resolve relative times to absolute times using TimeResolver
	endTime1, err := globalTimeResolver.ResolveRelativeTime(period1.EndTime, now)
	if err != nil {
		return 0, fmt.Errorf("failed to resolve end time for period %s: %v", period1.PeriodId, err)
	}

	startTime2, err := globalTimeResolver.ResolveRelativeTime(period2.StartTime, now)
	if err != nil {
		return 0, fmt.Errorf("failed to resolve start time for period %s: %v", period2.PeriodId, err)
	}

	// Parse resolved times
	start1, err := time.Parse("15:04", endTime1)
	if err != nil {
		return 0, fmt.Errorf("invalid resolved end time in period %s: %v", period1.PeriodId, err)
	}

	start2, err := time.Parse("15:04", startTime2)
	if err != nil {
		return 0, fmt.Errorf("invalid resolved start time in period %s: %v", period2.PeriodId, err)
	}

	// Calculate time difference in minutes
	timeDiff := start2.Sub(start1).Minutes()
	if timeDiff <= 0 {
		return 0, fmt.Errorf("period 2 must start after period 1")
	}

	// Get setpoint values
	sp1, exists1 := period1.Setpoints[setpointName]
	sp2, exists2 := period2.Setpoints[setpointName]
	if !exists1 || !exists2 {
		return 0, fmt.Errorf("setpoint %s not found in one or both periods", setpointName)
	}

	// Calculate setpoint difference
	spDiff := sp2 - sp1

	// Calculate ramp rate (change per minute)
	rampRate := spDiff / timeDiff

	return rampRate, nil
}

// DetectAndHandleOverlaps detects periods with less than 30 minutes gap and marks overlapping periods
func DetectAndHandleOverlaps(config *types.DiurnalSetpointConfig, clockState *types.ClockState) {
	// Set up time resolver for relative time calculations
	timeResolver := NewTimeResolver(clockState)
	now := time.Now()

	for instanceIdx := range config.Instances {
		instance := &config.Instances[instanceIdx]
		if !instance.Enabled || len(instance.Periods) < 2 {
			continue
		}

		// Sort periods by start time for each day
		for dayName := range map[string]bool{
			"monday": true, "tuesday": true, "wednesday": true, "thursday": true,
			"friday": true, "saturday": true, "sunday": true,
		} {
			var dayPeriods []*types.Period
			var dayIndices []int

			// Collect periods active on this day
			for i := range instance.Periods {
				period := &instance.Periods[i]
				if period.Enabled && period.ActiveDays[dayName] {
					dayPeriods = append(dayPeriods, period)
					dayIndices = append(dayIndices, i)
				}
			}

			if len(dayPeriods) < 2 {
				continue
			}

			// Sort periods by resolved start time
			for i := 0; i < len(dayPeriods)-1; i++ {
				for j := i + 1; j < len(dayPeriods); j++ {
					startTimeI, err1 := timeResolver.ResolveRelativeTime(dayPeriods[i].StartTime, now)
					startTimeJ, err2 := timeResolver.ResolveRelativeTime(dayPeriods[j].StartTime, now)
					if err1 == nil && err2 == nil {
						timeI, _ := time.Parse("15:04", startTimeI)
						timeJ, _ := time.Parse("15:04", startTimeJ)
						if timeI.After(timeJ) {
							dayPeriods[i], dayPeriods[j] = dayPeriods[j], dayPeriods[i]
							dayIndices[i], dayIndices[j] = dayIndices[j], dayIndices[i]
						}
					}
				}
			}

			// Check for overlaps between consecutive periods
			for i := 0; i < len(dayPeriods)-1; i++ {
				currentPeriod := dayPeriods[i]
				nextPeriod := dayPeriods[i+1]
				nextIndex := dayIndices[i+1]

				// Resolve end time of current period and start time of next period
				endTime, err1 := timeResolver.ResolveRelativeTime(currentPeriod.EndTime, now)
				startTime, err2 := timeResolver.ResolveRelativeTime(nextPeriod.StartTime, now)

				if err1 != nil || err2 != nil {
					continue
				}

				endTimeParsed, err1 := time.Parse("15:04", endTime)
				startTimeParsed, err2 := time.Parse("15:04", startTime)

				if err1 != nil || err2 != nil {
					continue
				}

				// Calculate time difference in minutes
				timeDiff := startTimeParsed.Sub(endTimeParsed).Minutes()

				// If gap is less than 30 minutes, mark next period as overlapped
				if timeDiff < 30 && timeDiff >= 0 {
					config.Instances[instanceIdx].Periods[nextIndex].PeriodStatus = "notUsedOverlap"
					log.Printf("Diurnal: Period %s in instance %s marked as 'notUsedOverlap' - gap of %.1f minutes with previous period on %s",
						nextPeriod.PeriodId, instance.InstanceId, timeDiff, dayName)
				}
			}
		}
	}
}

// ProcessInstances processes diurnal instances in parallel with optional clock state for relative times
func ProcessInstances(ctx context.Context, config types.DiurnalSetpointConfig, clockState *types.ClockState) {
	// Set global clock state for relative time calculations
	if clockState != nil {
		log.Printf("Diurnal: Using clock state for relative time calculations: Dawn=%s, Dusk=%s",
			clockState.Dawn, clockState.Dusk)
		setGlobalClockState(clockState)
	} else {
		log.Println("Diurnal: Warning - No clock state provided, using default dawn/dusk times")
	}

	// Detect and handle overlaps before processing
	configCopy := config // Make a copy to modify
	DetectAndHandleOverlaps(&configCopy, clockState)
	// Create a channel to receive results
	results := make(chan struct {
		instanceID string
		period     types.Period
		err        error
	})

	// Process each enabled instance in parallel
	for _, instance := range configCopy.Instances {
		if instance.Enabled {
			go func(inst types.DiurnalInstance) {
				for i, period := range inst.Periods {
					// Skip periods marked as overlapped
					if period.PeriodStatus == "notUsedOverlap" {
						log.Printf("Diurnal: Skipping period %s in instance %s - marked as overlapped",
							period.PeriodId, inst.InstanceId)
						results <- struct {
							instanceID string
							period     types.Period
							err        error
						}{inst.InstanceId, period, nil}
						continue
					}

					err := redis.StoreSetpoints(ctx, inst, period)
					if err != nil {
						results <- struct {
							instanceID string
							period     types.Period
							err        error
						}{inst.InstanceId, period, err}
						continue
					}

					// Calculate and store ramp rate if this is not the last period
					if i < len(inst.Periods)-1 {
						nextPeriod := inst.Periods[i+1]
						// Calculate ramp rate for each setpoint
						for setpointName := range period.Setpoints {
							rampRate, err := CalculateRampRate(period, nextPeriod, setpointName)
							if err != nil {
								log.Printf("Warning: Failed to calculate ramp rate for instance %s period %s setpoint %s: %v",
									inst.InstanceId, period.PeriodId, setpointName, err)
								continue
							}

							// Ramp rate calculated and logged (no legacy storage needed)
							log.Printf("Ramp rate for instance %s period %s setpoint %s: %.2f per minute",
								inst.InstanceId, period.PeriodId, setpointName, rampRate)
						}
					}

					results <- struct {
						instanceID string
						period     types.Period
						err        error
					}{inst.InstanceId, period, nil}
				}
			}(instance)
		}
	}

	// Collect results
	processedCount := 0
	totalPeriods := 0
	for _, instance := range configCopy.Instances {
		if instance.Enabled {
			totalPeriods += len(instance.Periods)
		}
	}

	// Track if we've printed the first period's end time
	firstPeriodPrinted := false

	for processedCount < totalPeriods {
		result := <-results
		processedCount++

		if result.err != nil {
			log.Printf("Warning: Failed to store setpoints for instance %s period %s in Redis: %v",
				result.instanceID, result.period.PeriodId, result.err)
		}

		// Print end time of first period
		if !firstPeriodPrinted {
			log.Printf("\nEnd time of first period: %s", result.period.EndTime)
			firstPeriodPrinted = true
		}
	}
}

// GetCurrentState determines the current state of a diurnal instance
func GetCurrentState(currentTime time.Time, instance types.DiurnalInstance) (types.PeriodState, *types.Period, *types.Period) {
	if !instance.Enabled {
		return types.StateIdle, nil, nil
	}

	// Check if currently in an active period
	for i := range instance.Periods {
		period := &instance.Periods[i]
		if period.Enabled && isTimeInPeriod(currentTime, *period) {
			return types.StateInPeriod, period, nil
		}
	}

	// Check if currently in a ramp between periods
	currentPeriod, nextPeriod := findRampPeriods(currentTime, instance)
	if currentPeriod != nil && nextPeriod != nil {
		return types.StateRamping, currentPeriod, nextPeriod
	}

	return types.StateIdle, nil, nil
}

// isTimeInPeriod checks if the current time falls within a period
func isTimeInPeriod(currentTime time.Time, period types.Period) bool {
	// Skip periods marked as overlapped
	if period.PeriodStatus == "notUsedOverlap" {
		return false
	}

	// Check day of week
	dayName := strings.ToLower(currentTime.Weekday().String())
	if !period.ActiveDays[dayName] {
		return false
	}

	// Use global time resolver for backward compatibility
	if globalTimeResolver == nil {
		globalTimeResolver = NewTimeResolver(getGlobalClockState())
	}

	// Resolve relative times to absolute times using TimeResolver
	resolvedStartTime, err := globalTimeResolver.ResolveRelativeTime(period.StartTime, currentTime)
	if err != nil {
		return false
	}
	resolvedEndTime, err := globalTimeResolver.ResolveRelativeTime(period.EndTime, currentTime)
	if err != nil {
		return false
	}

	// Parse resolved period times
	startTime, err := time.Parse("15:04", resolvedStartTime)
	if err != nil {
		return false
	}
	endTime, err := time.Parse("15:04", resolvedEndTime)
	if err != nil {
		return false
	}

	// Create time objects for comparison (same date, different times)
	currentTimeOfDay := time.Date(0, 1, 1, currentTime.Hour(), currentTime.Minute(), currentTime.Second(), 0, time.UTC)
	startTimeOfDay := time.Date(0, 1, 1, startTime.Hour(), startTime.Minute(), 0, 0, time.UTC)
	endTimeOfDay := time.Date(0, 1, 1, endTime.Hour(), endTime.Minute(), 0, 0, time.UTC)

	// Handle periods that cross midnight
	if endTimeOfDay.Before(startTimeOfDay) {
		// Period crosses midnight (e.g., 22:00 to 06:00)
		return currentTimeOfDay.After(startTimeOfDay) || currentTimeOfDay.Before(endTimeOfDay)
	}

	// Normal period within same day
	return currentTimeOfDay.After(startTimeOfDay) && currentTimeOfDay.Before(endTimeOfDay)
}

// findRampPeriods finds the current and next periods for ramping
func findRampPeriods(currentTime time.Time, instance types.DiurnalInstance) (*types.Period, *types.Period) {
	if !instance.Enabled || len(instance.Periods) < 2 {
		return nil, nil
	}

	// Get current day of week
	dayName := strings.ToLower(currentTime.Weekday().String())
	currentTimeOfDay := time.Date(0, 1, 1, currentTime.Hour(), currentTime.Minute(), currentTime.Second(), 0, time.UTC)

	// Find periods that are active today and sort them by start time
	var todaysPeriods []types.Period
	for _, period := range instance.Periods {
		if period.Enabled && period.ActiveDays[dayName] && period.PeriodStatus != "notUsedOverlap" {
			todaysPeriods = append(todaysPeriods, period)
		}
	}

	if len(todaysPeriods) < 2 {
		return nil, nil
	}

	// Use global time resolver for backward compatibility
	if globalTimeResolver == nil {
		globalTimeResolver = NewTimeResolver(getGlobalClockState())
	}

	// Sort periods by start time (resolve relative times first)
	for i := 0; i < len(todaysPeriods)-1; i++ {
		for j := i + 1; j < len(todaysPeriods); j++ {
			resolvedStartI, _ := globalTimeResolver.ResolveRelativeTime(todaysPeriods[i].StartTime, currentTime)
			resolvedStartJ, _ := globalTimeResolver.ResolveRelativeTime(todaysPeriods[j].StartTime, currentTime)
			startI, _ := time.Parse("15:04", resolvedStartI)
			startJ, _ := time.Parse("15:04", resolvedStartJ)
			if startI.After(startJ) {
				todaysPeriods[i], todaysPeriods[j] = todaysPeriods[j], todaysPeriods[i]
			}
		}
	}

	// Check if we're in a ramp between consecutive periods
	for i := 0; i < len(todaysPeriods)-1; i++ {
		currentPeriod := todaysPeriods[i]
		nextPeriod := todaysPeriods[i+1]

		// Resolve relative times to absolute times using TimeResolver
		resolvedEndTime, err := globalTimeResolver.ResolveRelativeTime(currentPeriod.EndTime, currentTime)
		if err != nil {
			continue
		}
		resolvedStartTime, err := globalTimeResolver.ResolveRelativeTime(nextPeriod.StartTime, currentTime)
		if err != nil {
			continue
		}

		endTime, err := time.Parse("15:04", resolvedEndTime)
		if err != nil {
			continue
		}
		startTime, err := time.Parse("15:04", resolvedStartTime)
		if err != nil {
			continue
		}

		endTimeOfDay := time.Date(0, 1, 1, endTime.Hour(), endTime.Minute(), 0, 0, time.UTC)
		startTimeOfDay := time.Date(0, 1, 1, startTime.Hour(), startTime.Minute(), 0, 0, time.UTC)

		// Check if current time is between end of current period and start of next period
		if currentTimeOfDay.After(endTimeOfDay) && currentTimeOfDay.Before(startTimeOfDay) {
			return &currentPeriod, &nextPeriod
		}
	}

	return nil, nil
}

// CalculateCurrentSetpoints calculates the current setpoint values for an instance
func CalculateCurrentSetpoints(currentTime time.Time, instance types.DiurnalInstance) (map[string]float64, error) {
	state, currentPeriod, nextPeriod := GetCurrentState(currentTime, instance)

	switch state {
	case types.StateInPeriod:
		// Return period setpoints directly
		return currentPeriod.Setpoints, nil

	case types.StateRamping:
		// Calculate ramped values
		result := make(map[string]float64)

		// Use global time resolver for backward compatibility
		if globalTimeResolver == nil {
			globalTimeResolver = NewTimeResolver(getGlobalClockState())
		}

		// Resolve relative time to absolute time using TimeResolver
		resolvedEndTime, err := globalTimeResolver.ResolveRelativeTime(currentPeriod.EndTime, currentTime)
		if err != nil {
			return nil, fmt.Errorf("failed to resolve end time: %v", err)
		}

		// Calculate minutes elapsed since end of current period
		endTime, err := time.Parse("15:04", resolvedEndTime)
		if err != nil {
			return nil, fmt.Errorf("invalid resolved end time format: %v", err)
		}

		endDateTime := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(),
			endTime.Hour(), endTime.Minute(), 0, 0, currentTime.Location())
		minutesElapsed := currentTime.Sub(endDateTime).Minutes()

		// Calculate ramped setpoints
		for setpointName, startValue := range currentPeriod.Setpoints {
			rampRate, err := CalculateRampRate(*currentPeriod, *nextPeriod, setpointName)
			if err != nil {
				return nil, fmt.Errorf("failed to calculate ramp rate for %s: %v", setpointName, err)
			}

			currentValue := startValue + (rampRate * minutesElapsed)
			result[setpointName] = currentValue
		}

		return result, nil

	case types.StateIdle:
		// Return empty map or default values
		return make(map[string]float64), nil

	default:
		return nil, fmt.Errorf("unknown state: %v", state)
	}
}

// GetDiurnalState returns the complete state information for an instance
func GetDiurnalState(currentTime time.Time, instance types.DiurnalInstance) types.DiurnalState {
	state, currentPeriod, nextPeriod := GetCurrentState(currentTime, instance)

	diurnalState := types.DiurnalState{
		InstanceID:    instance.InstanceId,
		State:         state,
		CurrentPeriod: currentPeriod,
		NextPeriod:    nextPeriod,
		LastUpdated:   currentTime.Format("15:04:05"),
	}

	// Calculate current setpoint values
	currentValues, err := CalculateCurrentSetpoints(currentTime, instance)
	if err != nil {
		log.Printf("Warning: Failed to calculate current setpoints for instance %s: %v", instance.InstanceId, err)
		currentValues = make(map[string]float64)
	}
	diurnalState.CurrentValues = currentValues

	// Calculate minutes elapsed if ramping
	if state == types.StateRamping && currentPeriod != nil {
		// Use global time resolver for backward compatibility
		if globalTimeResolver == nil {
			globalTimeResolver = NewTimeResolver(getGlobalClockState())
		}

		// Resolve relative time to absolute time using TimeResolver
		resolvedEndTime, err := globalTimeResolver.ResolveRelativeTime(currentPeriod.EndTime, currentTime)
		if err == nil {
			endTime, err := time.Parse("15:04", resolvedEndTime)
			if err == nil {
				endDateTime := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(),
					endTime.Hour(), endTime.Minute(), 0, 0, currentTime.Location())
				diurnalState.MinutesElapsed = currentTime.Sub(endDateTime).Minutes()
			}
		}
	}

	return diurnalState
}

// PrintPeriodOverlapStatus prints information about periods marked as overlapped
func PrintPeriodOverlapStatus(config types.DiurnalSetpointConfig) {
	log.Println("\n=== Period Overlap Status ===")

	hasOverlaps := false
	for _, instance := range config.Instances {
		if !instance.Enabled {
			continue
		}

		instanceHasOverlaps := false
		for _, period := range instance.Periods {
			if period.PeriodStatus == "notUsedOverlap" {
				if !instanceHasOverlaps {
					log.Printf("\nInstance %s (%s):", instance.InstanceId, instance.ProgramName)
					instanceHasOverlaps = true
					hasOverlaps = true
				}
				log.Printf("  Period %s (%s): %s - %s [STATUS: %s]",
					period.PeriodId, period.Name, period.StartTime, period.EndTime, period.PeriodStatus)
			}
		}
	}

	if !hasOverlaps {
		log.Println("No period overlaps detected - all periods have sufficient time gaps (≥30 minutes)")
	}
	log.Println("=============================")
}
